import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:local_auth/local_auth.dart';
import 'package:mocktail/mocktail.dart';
import 'package:water_metering/core/services/auth_service.dart';
import 'package:water_metering/core/services/auth_state_service.dart';
import 'package:water_metering/core/services/biometric_service.dart';
import 'package:water_metering/core/services/secure_storage_service.dart';
import 'package:water_metering/core/services/session_service.dart';
import 'package:water_metering/core/services/token_service.dart';
import 'package:water_metering/core/services/two_factor_service.dart';
import 'package:water_metering/data/datasources/auth_local_datasource.dart';
import 'package:water_metering/data/datasources/auth_remote_datasource.dart';
import 'package:water_metering/domain/repositories/auth_repository.dart';
import 'package:water_metering/domain/usecases/login_usecase.dart';
import 'package:water_metering/domain/usecases/logout_usecase.dart';
import 'package:water_metering/domain/usecases/verify_two_factor_usecase.dart';

/// Test helper file providing mock classes for authentication testing
///
/// This file provides manual mock implementations using mocktail for better
/// control over mock behavior and easier setup.
///
/// Import this file in your test files to access the mock classes.

// Mock classes using mocktail for better null safety support
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}

class MockHttpClient extends Mock implements http.Client {}

class MockLocalAuthentication extends Mock implements LocalAuthentication {}

// Core services mocks
class MockSecureStorageService extends Mock implements SecureStorageService {}

class MockTokenService extends Mock implements TokenService {}

class MockAuthService extends Mock implements AuthService {}

class MockSessionService extends Mock implements SessionService {}

class MockBiometricService extends Mock implements BiometricService {}

class MockTwoFactorService extends Mock implements TwoFactorService {}

class MockAuthStateService extends Mock implements AuthStateService {}

// Data sources mocks
class MockAuthLocalDataSource extends Mock implements AuthLocalDataSource {}

class MockAuthRemoteDataSource extends Mock implements AuthRemoteDataSource {}

// Domain layer mocks
class MockAuthRepository extends Mock implements AuthRepository {}

class MockLoginUseCase extends Mock implements LoginUseCase {}

class MockBiometricLoginUseCase extends Mock implements BiometricLoginUseCase {}

class MockLogoutUseCase extends Mock implements LogoutUseCase {}

class MockVerifyTwoFactorUseCase extends Mock
    implements VerifyTwoFactorUseCase {}
